<?php
/**
 * ملف اختبار للذكاء الاصطناعي
 */

require_once 'classes/OpenRouterAPI.php';
require_once 'classes/AIProcessor.php';

echo "<h1>اختبار الذكاء الاصطناعي</h1>";
echo "<style>body{font-family:Arial;direction:rtl;}</style>";

// اختبار الاتصال بـ OpenRouter API
echo "<h2>1. اختبار الاتصال بـ OpenRouter API</h2>";

$openRouter = new OpenRouterAPI();

$testMessage = "مرحبا، أريد اختبار الاتصال";
$response = $openRouter->sendChatRequest($testMessage);

if ($response['success']) {
    echo "<div style='color:green;'>✅ نجح الاتصال!</div>";
    echo "<p><strong>الرد:</strong> " . htmlspecialchars($response['content']) . "</p>";
} else {
    echo "<div style='color:red;'>❌ فشل الاتصال!</div>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($response['error']) . "</p>";
}

echo "<hr>";

// اختبار تحليل طلب قطعة غيار
echo "<h2>2. اختبار تحليل طلب قطعة غيار</h2>";

$testRequests = [
    "أبحث عن فرامل لتويوتا كامري 2015",
    "محتاج بطارية لمرسيدس C200 موديل 2018",
    "أريد إطارات للسيارة",
    "مرحبا كيف الحال؟",
    "كم سعر المحرك؟"
];

foreach ($testRequests as $i => $request) {
    echo "<h3>طلب " . ($i + 1) . ": " . htmlspecialchars($request) . "</h3>";
    
    $analysisResponse = $openRouter->analyzePartRequest($request);
    
    if ($analysisResponse['success']) {
        echo "<div style='color:green;'>✅ نجح التحليل!</div>";
        echo "<pre style='background:#f5f5f5;padding:10px;'>";
        print_r($analysisResponse['analysis']);
        echo "</pre>";
    } else {
        echo "<div style='color:red;'>❌ فشل التحليل!</div>";
        echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($analysisResponse['error']) . "</p>";
        if (isset($analysisResponse['raw_content'])) {
            echo "<p><strong>المحتوى الخام:</strong></p>";
            echo "<pre style='background:#ffe6e6;padding:10px;'>" . htmlspecialchars($analysisResponse['raw_content']) . "</pre>";
        }
    }
    
    echo "<hr>";
}

// اختبار AIProcessor المحدث
echo "<h2>3. اختبار AIProcessor المحدث</h2>";

$aiProcessor = new AIProcessor();

foreach ($testRequests as $i => $request) {
    echo "<h3>معالجة طلب " . ($i + 1) . ": " . htmlspecialchars($request) . "</h3>";
    
    try {
        // تحليل الرسالة
        $analysis = $aiProcessor->analyzeMessage($request);
        
        echo "<h4>التحليل:</h4>";
        echo "<pre style='background:#f0f8ff;padding:10px;'>";
        print_r($analysis);
        echo "</pre>";
        
        // توليد الرد
        $response = $aiProcessor->generateResponse($analysis);
        
        echo "<h4>الرد المولد:</h4>";
        echo "<div style='background:#f0fff0;padding:10px;border:1px solid #ccc;'>";
        echo nl2br(htmlspecialchars($response));
        echo "</div>";
        
        // التحقق من اكتمال المعلومات
        $isComplete = $aiProcessor->isInformationComplete($analysis);
        echo "<h4>اكتمال المعلومات:</h4>";
        echo "<div style='color:" . ($isComplete ? 'green' : 'orange') . ";'>";
        echo $isComplete ? "✅ مكتملة" : "⚠️ ناقصة";
        echo "</div>";
        
        if (!$isComplete) {
            $missingQuestions = $aiProcessor->generateMissingInfoQuestions($analysis);
            echo "<h4>الأسئلة المطلوبة:</h4>";
            echo "<ul>";
            foreach ($missingQuestions as $question) {
                echo "<li>" . htmlspecialchars($question) . "</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color:red;'>❌ خطأ في المعالجة: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "<hr>";
}

// اختبار تلخيص ردود المحلات
echo "<h2>4. اختبار تلخيص ردود المحلات</h2>";

$sampleResponses = [
    [
        'shop_name' => 'محل الأمين لقطع الغيار',
        'shop_phone' => '01234567890',
        'shop_address' => 'شارع الملك فهد، الرياض',
        'has_part' => true,
        'price' => 250.00,
        'delivery_time' => 'متوفر فوراً',
        'availability_notes' => 'قطعة أصلية مضمونة'
    ],
    [
        'shop_name' => 'قطع غيار الخليج',
        'shop_phone' => '01234567891',
        'shop_address' => 'طريق الملك عبدالعزيز، جدة',
        'has_part' => false,
        'price' => null,
        'delivery_time' => null,
        'availability_notes' => 'غير متوفر حالياً، يمكن طلبه خلال أسبوع'
    ],
    [
        'shop_name' => 'مركز النور للسيارات',
        'shop_phone' => '01234567892',
        'shop_address' => 'شارع العليا، الدمام',
        'has_part' => true,
        'price' => 220.00,
        'delivery_time' => 'خلال ساعتين',
        'availability_notes' => 'قطعة تجارية جودة عالية'
    ]
];

$summary = $aiProcessor->summarizeShopResponses($sampleResponses);

echo "<h3>ملخص الردود:</h3>";
echo "<div style='background:#fff8dc;padding:15px;border:1px solid #ddd;'>";
echo nl2br(htmlspecialchars($summary));
echo "</div>";

echo "<hr>";
echo "<h2>انتهى الاختبار</h2>";
echo "<p>تحقق من النتائج أعلاه للتأكد من عمل جميع المكونات بشكل صحيح.</p>";
?>
