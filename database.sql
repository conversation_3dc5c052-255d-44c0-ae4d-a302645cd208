-- قاعدة بيانات شات بوت قطع غيار السيارات
CREATE DATABASE IF NOT EXISTS auto_parts_chatbot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE auto_parts_chatbot;

-- جدول المحلات المسجلة
CREATE TABLE shops (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    owner_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    address TEXT,
    specialties TEXT, -- تخصصات المحل (مرسيدس، بي ام دبليو، إلخ)
    is_active BOOLEAN DEFAULT TRUE,
    notification_method ENUM('email', 'sms', 'whatsapp', 'phone') DEFAULT 'phone',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المحادثات مع العملاء
CREATE TABLE conversations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_name VARCHAR(255),
    customer_phone VARCHAR(20),
    customer_email VARCHAR(255),
    status ENUM('active', 'waiting_for_shops', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول طلبات قطع الغيار
CREATE TABLE part_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    car_brand VARCHAR(100), -- ماركة السيارة
    car_model VARCHAR(100), -- موديل السيارة
    car_year YEAR, -- سنة الصنع
    part_name VARCHAR(255) NOT NULL, -- اسم القطعة
    part_description TEXT, -- وصف إضافي للقطعة
    part_number VARCHAR(100), -- رقم القطعة إن وجد
    urgency ENUM('low', 'medium', 'high') DEFAULT 'medium',
    max_budget DECIMAL(10,2), -- الميزانية القصوى
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- جدول الرسائل في المحادثة
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    sender_type ENUM('customer', 'bot', 'admin') NOT NULL,
    message_text TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- جدول إشعارات المحلات
CREATE TABLE shop_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shop_id INT NOT NULL,
    part_request_id INT NOT NULL,
    notification_sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notification_method VARCHAR(50),
    status ENUM('sent', 'delivered', 'read', 'responded') DEFAULT 'sent',
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    FOREIGN KEY (part_request_id) REFERENCES part_requests(id) ON DELETE CASCADE
);

-- جدول ردود المحلات
CREATE TABLE shop_responses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shop_id INT NOT NULL,
    part_request_id INT NOT NULL,
    has_part BOOLEAN NOT NULL, -- هل القطعة متوفرة
    price DECIMAL(10,2), -- السعر إذا كانت متوفرة
    availability_notes TEXT, -- ملاحظات حول التوفر
    delivery_time VARCHAR(100), -- وقت التوصيل المتوقع
    contact_info TEXT, -- معلومات التواصل الإضافية
    response_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    FOREIGN KEY (part_request_id) REFERENCES part_requests(id) ON DELETE CASCADE
);

-- إدراج بيانات تجريبية للمحلات
INSERT INTO shops (name, owner_name, phone, email, address, specialties, notification_method) VALUES
('محل الأمين لقطع الغيار', 'أحمد محمد', '01234567890', '<EMAIL>', 'شارع الملك فهد، الرياض', 'تويوتا، نيسان، هونداي', 'phone'),
('قطع غيار الخليج', 'محمد علي', '01234567891', '<EMAIL>', 'طريق الملك عبدالعزيز، جدة', 'مرسيدس، بي ام دبليو، أودي', 'whatsapp'),
('مركز النور للسيارات', 'عبدالله أحمد', '01234567892', '<EMAIL>', 'شارع العليا، الدمام', 'فورد، شيفروليه، جي ام سي', 'phone'),
('محل الفهد للقطع', 'فهد سعد', '01234567893', '<EMAIL>', 'حي الملز، الرياض', 'كيا، هيونداي، ميتسوبيشي', 'sms'),
('قطع غيار الشرق', 'سعد محمد', '01234567894', '<EMAIL>', 'الكورنيش، الخبر', 'لكزس، إنفينيتي، أكورا', 'email');
