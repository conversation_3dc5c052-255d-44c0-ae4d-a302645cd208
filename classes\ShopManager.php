<?php
require_once 'config/database.php';

/**
 * إدارة المحلات والتواصل معها
 */
class ShopManager {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * الحصول على جميع المحلات النشطة
     */
    public function getActiveShops() {
        $sql = "SELECT * FROM shops WHERE is_active = 1 ORDER BY name";
        return $this->db->fetchAll($sql);
    }

    /**
     * الحصول على محل بالمعرف
     */
    public function getShop($shopId) {
        $sql = "SELECT * FROM shops WHERE id = :id";
        return $this->db->fetchOne($sql, ['id' => $shopId]);
    }

    /**
     * إرسال إشعار للمحلات حول طلب قطعة غيار
     */
    public function notifyShopsAboutRequest($partRequestId) {
        // الحصول على تفاصيل الطلب
        $request = $this->getPartRequestDetails($partRequestId);
        if (!$request) {
            return false;
        }

        // الحصول على المحلات المناسبة
        $shops = $this->getRelevantShops($request);
        
        $notificationsSent = 0;
        foreach ($shops as $shop) {
            if ($this->sendNotificationToShop($shop, $request)) {
                $notificationsSent++;
            }
        }

        return $notificationsSent;
    }

    /**
     * الحصول على تفاصيل طلب قطعة الغيار
     */
    private function getPartRequestDetails($partRequestId) {
        $sql = "SELECT pr.*, c.customer_name, c.customer_phone 
                FROM part_requests pr 
                JOIN conversations c ON pr.conversation_id = c.id 
                WHERE pr.id = :id";
        
        return $this->db->fetchOne($sql, ['id' => $partRequestId]);
    }

    /**
     * الحصول على المحلات المناسبة للطلب
     */
    private function getRelevantShops($request) {
        // يمكن تحسين هذا لاحقاً لفلترة المحلات حسب التخصص
        return $this->getActiveShops();
    }

    /**
     * إرسال إشعار لمحل واحد
     */
    private function sendNotificationToShop($shop, $request) {
        // تسجيل الإشعار في قاعدة البيانات
        $notificationData = [
            'shop_id' => $shop['id'],
            'part_request_id' => $request['id'],
            'notification_method' => $shop['notification_method'],
            'status' => 'sent'
        ];

        $notificationId = $this->db->insert('shop_notifications', $notificationData);

        // إرسال الإشعار حسب الطريقة المفضلة
        switch ($shop['notification_method']) {
            case 'phone':
                return $this->sendPhoneNotification($shop, $request);
            case 'sms':
                return $this->sendSMSNotification($shop, $request);
            case 'whatsapp':
                return $this->sendWhatsAppNotification($shop, $request);
            case 'email':
                return $this->sendEmailNotification($shop, $request);
            default:
                return false;
        }
    }

    /**
     * إرسال إشعار هاتفي (محاكاة)
     */
    private function sendPhoneNotification($shop, $request) {
        // هنا يمكن دمج API للمكالمات الآلية
        error_log("Phone notification sent to {$shop['name']} at {$shop['phone']} for part: {$request['part_name']}");
        return true;
    }

    /**
     * إرسال رسالة نصية (محاكاة)
     */
    private function sendSMSNotification($shop, $request) {
        $message = "طلب قطعة غيار جديد: {$request['part_name']} للسيارة {$request['car_brand']} {$request['car_model']} {$request['car_year']}. للرد: [رابط الموقع]";
        
        // هنا يمكن دمج API لإرسال الرسائل النصية
        error_log("SMS sent to {$shop['phone']}: $message");
        return true;
    }

    /**
     * إرسال رسالة واتساب (محاكاة)
     */
    private function sendWhatsAppNotification($shop, $request) {
        $message = "🔧 طلب قطعة غيار جديد\n";
        $message .= "القطعة: {$request['part_name']}\n";
        $message .= "السيارة: {$request['car_brand']} {$request['car_model']} {$request['car_year']}\n";
        $message .= "للرد على الطلب، يرجى زيارة الرابط: [رابط الموقع]";
        
        // هنا يمكن دمج WhatsApp Business API
        error_log("WhatsApp sent to {$shop['phone']}: $message");
        return true;
    }

    /**
     * إرسال بريد إلكتروني (محاكاة)
     */
    private function sendEmailNotification($shop, $request) {
        $subject = "طلب قطعة غيار جديد - {$request['part_name']}";
        $body = "عزيزي {$shop['owner_name']},\n\n";
        $body .= "تم استلام طلب جديد لقطعة غيار:\n";
        $body .= "القطعة المطلوبة: {$request['part_name']}\n";
        $body .= "السيارة: {$request['car_brand']} {$request['car_model']} {$request['car_year']}\n";
        $body .= "وصف إضافي: {$request['part_description']}\n\n";
        $body .= "للرد على هذا الطلب، يرجى زيارة لوحة التحكم الخاصة بك.\n\n";
        $body .= "شكراً لك";

        // هنا يمكن استخدام mail() أو PHPMailer
        error_log("Email sent to {$shop['email']}: $subject");
        return true;
    }

    /**
     * تسجيل رد المحل على الطلب
     */
    public function recordShopResponse($shopId, $partRequestId, $responseData) {
        $data = [
            'shop_id' => $shopId,
            'part_request_id' => $partRequestId,
            'has_part' => $responseData['has_part'],
            'price' => $responseData['price'] ?? null,
            'availability_notes' => $responseData['notes'] ?? null,
            'delivery_time' => $responseData['delivery_time'] ?? null,
            'contact_info' => $responseData['contact_info'] ?? null
        ];

        $responseId = $this->db->insert('shop_responses', $data);

        // تحديث حالة الإشعار
        $this->updateNotificationStatus($shopId, $partRequestId, 'responded');

        return $responseId;
    }

    /**
     * تحديث حالة الإشعار
     */
    private function updateNotificationStatus($shopId, $partRequestId, $status) {
        return $this->db->update('shop_notifications',
            ['status' => $status],
            'shop_id = :shop_id AND part_request_id = :part_request_id',
            ['shop_id' => $shopId, 'part_request_id' => $partRequestId]
        );
    }

    /**
     * الحصول على ردود المحلات لطلب معين
     */
    public function getShopResponses($partRequestId) {
        $sql = "SELECT sr.*, s.name as shop_name, s.phone as shop_phone, s.address as shop_address
                FROM shop_responses sr
                JOIN shops s ON sr.shop_id = s.id
                WHERE sr.part_request_id = :part_request_id
                ORDER BY sr.response_date ASC";

        return $this->db->fetchAll($sql, ['part_request_id' => $partRequestId]);
    }
}
?>
