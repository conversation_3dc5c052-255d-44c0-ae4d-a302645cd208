<?php
require_once 'config/database.php';

/**
 * إدارة المحادثات مع العملاء
 */
class ConversationManager {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * بدء محادثة جديدة
     */
    public function startConversation($customerData = []) {
        $data = [
            'customer_name' => $customerData['name'] ?? null,
            'customer_phone' => $customerData['phone'] ?? null,
            'customer_email' => $customerData['email'] ?? null,
            'status' => 'active'
        ];

        $conversationId = $this->db->insert('conversations', $data);
        
        // إضافة رسالة ترحيب
        $this->addMessage($conversationId, 'bot', 'مرحباً بك في خدمة البحث عن قطع غيار السيارات! كيف يمكنني مساعدتك اليوم؟');
        
        return $conversationId;
    }

    /**
     * إضافة رسالة للمحادثة
     */
    public function addMessage($conversationId, $senderType, $messageText, $messageType = 'text') {
        $data = [
            'conversation_id' => $conversationId,
            'sender_type' => $senderType,
            'message_text' => $messageText,
            'message_type' => $messageType
        ];

        return $this->db->insert('messages', $data);
    }

    /**
     * الحصول على رسائل المحادثة
     */
    public function getMessages($conversationId, $limit = 50) {
        $sql = "SELECT * FROM messages WHERE conversation_id = :conversation_id 
                ORDER BY created_at ASC LIMIT :limit";
        
        return $this->db->fetchAll($sql, [
            'conversation_id' => $conversationId,
            'limit' => $limit
        ]);
    }

    /**
     * تحديث حالة المحادثة
     */
    public function updateConversationStatus($conversationId, $status) {
        return $this->db->update('conversations', 
            ['status' => $status], 
            'id = :id', 
            ['id' => $conversationId]
        );
    }

    /**
     * الحصول على تفاصيل المحادثة
     */
    public function getConversation($conversationId) {
        $sql = "SELECT * FROM conversations WHERE id = :id";
        return $this->db->fetchOne($sql, ['id' => $conversationId]);
    }

    /**
     * إنشاء طلب قطعة غيار
     */
    public function createPartRequest($conversationId, $partData) {
        $data = [
            'conversation_id' => $conversationId,
            'car_brand' => $partData['car_brand'] ?? null,
            'car_model' => $partData['car_model'] ?? null,
            'car_year' => $partData['car_year'] ?? null,
            'part_name' => $partData['part_name'],
            'part_description' => $partData['part_description'] ?? null,
            'part_number' => $partData['part_number'] ?? null,
            'urgency' => $partData['urgency'] ?? 'medium',
            'max_budget' => $partData['max_budget'] ?? null
        ];

        $requestId = $this->db->insert('part_requests', $data);
        
        // تحديث حالة المحادثة
        $this->updateConversationStatus($conversationId, 'waiting_for_shops');
        
        return $requestId;
    }

    /**
     * الحصول على طلبات قطع الغيار للمحادثة
     */
    public function getPartRequests($conversationId) {
        $sql = "SELECT * FROM part_requests WHERE conversation_id = :conversation_id 
                ORDER BY created_at DESC";
        
        return $this->db->fetchAll($sql, ['conversation_id' => $conversationId]);
    }

    /**
     * الحصول على المحادثات النشطة
     */
    public function getActiveConversations() {
        $sql = "SELECT c.*, COUNT(m.id) as message_count 
                FROM conversations c 
                LEFT JOIN messages m ON c.id = m.conversation_id 
                WHERE c.status IN ('active', 'waiting_for_shops') 
                GROUP BY c.id 
                ORDER BY c.updated_at DESC";
        
        return $this->db->fetchAll($sql);
    }
}
?>
