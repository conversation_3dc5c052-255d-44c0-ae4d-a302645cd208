<?php
/**
 * إعدادات الذكاء الاصطناعي - OpenRouter API
 */

class AIConfig {
    // إعدادات OpenRouter API
    const API_BASE_URL = 'https://openrouter.ai/api/v1';
    const API_KEY = '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';

    // النماذج المتاحة (جرب نماذج مختلفة إذا واجهت مشاكل)
    const MODEL = 'qwen/qwen3-4b:free'; // النموذج المحدث
    // const MODEL = 'meta-llama/llama-3.2-3b-instruct:free'; // بديل 1
    // const MODEL = 'google/gemma-3n-e2b-it:free'; // بديل 2
    // const MODEL = 'microsoft/phi-3-mini-128k-instruct:free'; // بديل 3
    // const MODEL = 'huggingfaceh4/zephyr-7b-beta:free'; // بديل 4
    
    // إعدادات الموقع (اختيارية)
    const SITE_URL = 'http://localhost/ai';
    const SITE_NAME = 'Auto Parts Chatbot';
    
    // إعدادات النموذج
    const MAX_TOKENS = 1000;
    const TEMPERATURE = 0.7;
    
    /**
     * الحصول على headers للطلبات
     */
    public static function getHeaders() {
        return [
            'Authorization: Bearer ' . self::API_KEY,
            'Content-Type: application/json',
            'HTTP-Referer: ' . self::SITE_URL,
            'X-Title: ' . self::SITE_NAME
        ];
    }
    
    /**
     * الحصول على إعدادات النموذج الافتراضية
     */
    public static function getDefaultModelSettings() {
        return [
            'model' => self::MODEL,
            'max_tokens' => self::MAX_TOKENS,
            'temperature' => self::TEMPERATURE,
            'stream' => false
        ];
    }
}
?>
