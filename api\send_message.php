<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../classes/ConversationManager.php';
require_once '../classes/AIProcessor.php';
require_once '../classes/ShopManager.php';

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['conversation_id']) || !isset($input['message'])) {
        throw new Exception('بيانات غير مكتملة');
    }
    
    $conversationId = $input['conversation_id'];
    $message = trim($input['message']);
    
    if (empty($message)) {
        throw new Exception('الرسالة فارغة');
    }
    
    // إنشاء الكائنات المطلوبة
    $conversationManager = new ConversationManager();
    $aiProcessor = new AIProcessor();
    $shopManager = new ShopManager();
    
    // إضافة رسالة العميل
    $conversationManager->addMessage($conversationId, 'customer', $message);
    
    // تحليل الرسالة بالذكاء الاصطناعي
    $analysis = $aiProcessor->analyzeMessage($message);
    
    // توليد الرد
    $botResponse = $aiProcessor->generateResponse($analysis);
    
    // متغير لتتبع ما إذا تم إرسال الطلب للمحلات
    $requestSentToShops = false;
    
    // التحقق من اكتمال المعلومات
    if ($aiProcessor->isInformationComplete($analysis)) {
        // إنشاء طلب قطعة غيار
        $partData = [
            'car_brand' => $analysis['car_info']['brand'],
            'car_model' => $analysis['car_info']['model'],
            'car_year' => $analysis['car_info']['year'],
            'part_name' => $analysis['part_info']['name'],
            'part_description' => $analysis['part_info']['description'],
            'part_number' => $analysis['part_info']['number'],
            'urgency' => $analysis['urgency'],
            'max_budget' => $analysis['budget']
        ];
        
        $partRequestId = $conversationManager->createPartRequest($conversationId, $partData);
        
        // إرسال إشعارات للمحلات
        $notificationsSent = $shopManager->notifyShopsAboutRequest($partRequestId);
        
        if ($notificationsSent > 0) {
            $botResponse = "ممتاز! تم فهم طلبك بوضوح:\n\n";
            $botResponse .= "🚗 السيارة: {$analysis['car_info']['brand']}";
            if ($analysis['car_info']['model']) {
                $botResponse .= " {$analysis['car_info']['model']}";
            }
            if ($analysis['car_info']['year']) {
                $botResponse .= " ({$analysis['car_info']['year']})";
            }
            $botResponse .= "\n🔧 القطعة: {$analysis['part_info']['name']}\n\n";
            $botResponse .= "تم إرسال طلبك إلى {$notificationsSent} محل متخصص. ";
            $botResponse .= "سنعلمك فور وصول أي ردود من المحلات! 📱";
            
            $requestSentToShops = true;
        } else {
            $botResponse = "تم فهم طلبك، لكن حدث خطأ في إرسال الإشعارات للمحلات. يرجى المحاولة مرة أخرى.";
        }
    } else {
        // طلب المعلومات الناقصة
        $missingQuestions = $aiProcessor->generateMissingInfoQuestions($analysis);
        if (!empty($missingQuestions)) {
            $botResponse .= "\n\nلإكمال طلبك، أحتاج المعلومات التالية:\n";
            foreach ($missingQuestions as $question) {
                $botResponse .= "• " . $question . "\n";
            }
        }
    }
    
    // إضافة رد البوت
    $conversationManager->addMessage($conversationId, 'bot', $botResponse);
    
    echo json_encode([
        'success' => true,
        'bot_response' => $botResponse,
        'analysis' => $analysis,
        'request_sent_to_shops' => $requestSentToShops
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في معالجة الرسالة: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
