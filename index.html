<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شات بوت قطع غيار السيارات</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
        }

        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 18px;
            border-bottom-left-radius: 5px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .quick-action {
            padding: 8px 12px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quick-action:hover {
            background: #667eea;
            color: white;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="status-indicator"></div>
            <h1><i class="fas fa-car"></i> مساعد قطع غيار السيارات</h1>
            <p>نساعدك في العثور على قطع الغيار من أفضل المحلات</p>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-content">
                    مرحباً بك! 👋<br>
                    أنا مساعدك الذكي للبحث عن قطع غيار السيارات.<br>
                    أخبرني عن السيارة والقطعة التي تحتاجها وسأتواصل مع المحلات المناسبة للبحث عنها.
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>
            
            <div class="quick-actions">
                <div class="quick-action" onclick="sendQuickMessage('أبحث عن فرامل')">🔧 فرامل</div>
                <div class="quick-action" onclick="sendQuickMessage('أحتاج بطارية')">🔋 بطارية</div>
                <div class="quick-action" onclick="sendQuickMessage('أريد إطارات')">🛞 إطارات</div>
                <div class="quick-action" onclick="sendQuickMessage('محرك')">⚙️ محرك</div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="messageInput" 
                    placeholder="اكتب رسالتك هنا... مثال: أبحث عن فرامل لتويوتا كامري 2015"
                    autocomplete="off"
                >
                <button type="submit" class="send-button" id="sendButton">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </form>
        </div>
    </div>

    <script>
        let conversationId = null;
        let isWaitingForResponse = false;

        // تهيئة الوقت للرسالة الترحيبية
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // بدء محادثة جديدة عند تحميل الصفحة
        window.addEventListener('load', function() {
            startNewConversation();
        });

        // معالج إرسال الرسائل
        document.getElementById('chatForm').addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });

        // إرسال رسالة سريعة
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // بدء محادثة جديدة
        async function startNewConversation() {
            try {
                const response = await fetch('api/start_conversation.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                if (data.success) {
                    conversationId = data.conversation_id;
                }
            } catch (error) {
                console.error('Error starting conversation:', error);
            }
        }

        // إرسال رسالة
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || isWaitingForResponse) return;

            // إضافة رسالة المستخدم
            addMessage(message, 'user');
            messageInput.value = '';
            
            // إظهار مؤشر الكتابة
            showTypingIndicator();
            isWaitingForResponse = true;
            
            try {
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        conversation_id: conversationId,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                // إخفاء مؤشر الكتابة
                hideTypingIndicator();
                
                if (data.success) {
                    // إضافة رد البوت
                    addMessage(data.bot_response, 'bot');
                    
                    // إذا تم إرسال الطلب للمحلات
                    if (data.request_sent_to_shops) {
                        setTimeout(() => {
                            addMessage('تم إرسال طلبك إلى المحلات المناسبة. سنعلمك بالردود فور وصولها! 📱', 'bot');
                        }, 1000);
                    }
                } else {
                    addMessage('عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.', 'bot');
                }
            } catch (error) {
                hideTypingIndicator();
                addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'bot');
                console.error('Error sending message:', error);
            }
            
            isWaitingForResponse = false;
        }

        // إضافة رسالة للمحادثة
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const currentTime = new Date().toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${text}
                    <div class="message-time">${currentTime}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إظهار مؤشر الكتابة
        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            const messagesContainer = document.getElementById('chatMessages');
            
            indicator.style.display = 'block';
            messagesContainer.appendChild(indicator);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إخفاء مؤشر الكتابة
        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'none';
        }

        // التحقق من الردود الجديدة كل 30 ثانية
        setInterval(checkForNewResponses, 30000);

        async function checkForNewResponses() {
            if (!conversationId) return;
            
            try {
                const response = await fetch(`api/check_responses.php?conversation_id=${conversationId}`);
                const data = await response.json();
                
                if (data.success && data.new_responses && data.new_responses.length > 0) {
                    data.new_responses.forEach(response => {
                        addMessage(response.message, 'bot');
                    });
                }
            } catch (error) {
                console.error('Error checking responses:', error);
            }
        }
    </script>
</body>
</html>
