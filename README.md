# شات بوت قطع غيار السيارات 🚗🔧

نظام ذكي للبحث عن قطع غيار السيارات يربط بين العملاء والمحلات المتخصصة.

## المميزات الرئيسية ✨

- **شات بوت ذكي** يفهم طلبات العملاء باللغة العربية والإنجليزية
- **تحليل ذكي للرسائل** لاستخراج تفاصيل السيارة والقطعة المطلوبة
- **إشعارات تلقائية للمحلات** عبر الهاتف، SMS، واتساب، أو البريد الإلكتروني
- **لوحة تحكم للمحلات** للرد على الطلبات
- **واجهة عصرية وسهلة الاستخدام**
- **دعم كامل للغة العربية**

## متطلبات التشغيل 📋

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- XAMPP أو WAMP أو LAMP

## طريقة التثبيت 🚀

### 1. إعداد قاعدة البيانات

```sql
-- تشغيل ملف database.sql في phpMyAdmin أو MySQL
mysql -u root -p < database.sql
```

### 2. تكوين قاعدة البيانات

عدّل ملف `config/database.php` وأدخل بيانات قاعدة البيانات:

```php
private $host = 'localhost';
private $db_name = 'auto_parts_chatbot';
private $username = 'root';
private $password = 'كلمة_المرور_الخاصة_بك';
```

### 3. رفع الملفات

انسخ جميع الملفات إلى مجلد الخادم:
- XAMPP: `C:\xampp\htdocs\ai\`
- WAMP: `C:\wamp64\www\ai\`

### 4. تشغيل النظام

افتح المتصفح وانتقل إلى:
- **الشات بوت للعملاء**: `http://localhost/ai/`
- **لوحة تحكم المحلات**: `http://localhost/ai/shop_dashboard.php`

## كيفية الاستخدام 📱

### للعملاء:

1. افتح الشات بوت
2. اكتب طلبك مثل: "أبحث عن فرامل لتويوتا كامري 2015"
3. أجب على أسئلة البوت لتحديد التفاصيل
4. انتظر ردود المحلات

### لأصحاب المحلات:

1. ادخل إلى لوحة التحكم
2. اختر محلك من القائمة
3. شاهد الطلبات الجديدة
4. رد على كل طلب بتوفر القطعة والسعر

## هيكل المشروع 📁

```
ai/
├── index.html              # واجهة الشات بوت
├── shop_dashboard.php      # لوحة تحكم المحلات
├── database.sql           # قاعدة البيانات
├── config/
│   └── database.php       # إعدادات قاعدة البيانات
├── classes/
│   ├── ConversationManager.php  # إدارة المحادثات
│   ├── ShopManager.php          # إدارة المحلات
│   └── AIProcessor.php          # معالج الذكاء الاصطناعي
└── api/
    ├── start_conversation.php   # بدء محادثة جديدة
    ├── send_message.php         # إرسال رسالة
    └── check_responses.php      # التحقق من الردود
```

## قاعدة البيانات 🗄️

### الجداول الرئيسية:

- **shops**: معلومات المحلات المسجلة
- **conversations**: المحادثات مع العملاء
- **part_requests**: طلبات قطع الغيار
- **messages**: رسائل المحادثات
- **shop_notifications**: إشعارات المحلات
- **shop_responses**: ردود المحلات

## الذكاء الاصطناعي 🤖

النظام يستخدم معالجة ذكية للنصوص لـ:

- **تحديد نية العميل** (بحث، سؤال، تحية)
- **استخراج معلومات السيارة** (الماركة، الموديل، السنة)
- **تحديد القطعة المطلوبة**
- **تقدير مستوى الأولوية**
- **استخراج الميزانية المتوقعة**

## التطوير المستقبلي 🔮

- [ ] دمج APIs حقيقية للإشعارات (SMS, WhatsApp)
- [ ] نظام تقييم المحلات
- [ ] دعم الصور لقطع الغيار
- [ ] تطبيق موبايل
- [ ] نظام دفع إلكتروني
- [ ] ذكاء اصطناعي أكثر تطوراً
- [ ] دعم لغات إضافية

## الأمان 🔒

- تشفير كلمات المرور
- حماية من SQL Injection
- تنظيف المدخلات
- جلسات آمنة

## الدعم الفني 💬

للمساعدة أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

**تم تطويره بـ ❤️ لخدمة قطاع قطع غيار السيارات**
