<?php

/**
 * معالج الذكاء الاصطناعي لفهم رسائل العملاء
 */
class AIProcessor {
    private $carBrands = [
        'تويوتا', 'toyota', 'نيسان', 'nissan', 'هونداي', 'hyundai', 'كيا', 'kia',
        'مرسيدس', 'mercedes', 'بي ام دبليو', 'bmw', 'أودي', 'audi', 'فولكس واجن', 'volkswagen',
        'فورد', 'ford', 'شيفروليه', 'chevrolet', 'جي ام سي', 'gmc', 'دودج', 'dodge',
        'لكزس', 'lexus', 'إنفينيتي', 'infiniti', 'أكورا', 'acura', 'هوندا', 'honda',
        'ميتسوبيشي', 'mitsubishi', 'سوبارو', 'subaru', 'مازدا', 'mazda'
    ];

    private $commonParts = [
        'فرامل', 'brake', 'محرك', 'engine', 'جير', 'transmission', 'كلتش', 'clutch',
        'بطارية', 'battery', 'إطار', 'tire', 'زيت', 'oil', 'فلتر', 'filter',
        'شمعة', 'spark plug', 'رديتر', 'radiator', 'مكيف', 'ac', 'تكييف',
        'مصباح', 'light', 'مرآة', 'mirror', 'زجاج', 'glass', 'باب', 'door',
        'شنطة', 'trunk', 'كبوت', 'hood', 'مقعد', 'seat', 'عجلة قيادة', 'steering wheel',
        'عادم', 'exhaust', 'سستم', 'suspension', 'أموتيسور', 'shock absorber'
    ];

    /**
     * تحليل رسالة العميل واستخراج المعلومات
     */
    public function analyzeMessage($message) {
        $message = $this->normalizeText($message);
        
        $analysis = [
            'intent' => $this->detectIntent($message),
            'car_info' => $this->extractCarInfo($message),
            'part_info' => $this->extractPartInfo($message),
            'urgency' => $this->detectUrgency($message),
            'budget' => $this->extractBudget($message),
            'confidence' => 0.8
        ];

        return $analysis;
    }

    /**
     * تطبيع النص
     */
    private function normalizeText($text) {
        // تحويل للأحرف الصغيرة وإزالة علامات الترقيم الزائدة
        $text = mb_strtolower($text, 'UTF-8');
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * تحديد نية العميل
     */
    private function detectIntent($message) {
        $searchKeywords = ['أبحث', 'أريد', 'محتاج', 'بدي', 'عايز', 'أطلب', 'search', 'need', 'want'];
        $greetingKeywords = ['مرحبا', 'السلام', 'أهلا', 'hello', 'hi'];
        $questionKeywords = ['كم', 'متى', 'أين', 'how', 'when', 'where', 'what'];

        foreach ($searchKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return 'search_part';
            }
        }

        foreach ($greetingKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return 'greeting';
            }
        }

        foreach ($questionKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return 'question';
            }
        }

        return 'search_part'; // افتراضي
    }

    /**
     * استخراج معلومات السيارة
     */
    private function extractCarInfo($message) {
        $carInfo = [
            'brand' => null,
            'model' => null,
            'year' => null
        ];

        // البحث عن الماركة
        foreach ($this->carBrands as $brand) {
            if (strpos($message, $brand) !== false) {
                $carInfo['brand'] = $brand;
                break;
            }
        }

        // البحث عن السنة
        preg_match('/\b(19|20)\d{2}\b/', $message, $yearMatches);
        if (!empty($yearMatches)) {
            $carInfo['year'] = $yearMatches[0];
        }

        // محاولة استخراج الموديل (مبسطة)
        $words = explode(' ', $message);
        foreach ($words as $i => $word) {
            if ($word === $carInfo['brand'] && isset($words[$i + 1])) {
                $possibleModel = $words[$i + 1];
                if (!is_numeric($possibleModel) && strlen($possibleModel) > 2) {
                    $carInfo['model'] = $possibleModel;
                    break;
                }
            }
        }

        return $carInfo;
    }

    /**
     * استخراج معلومات القطعة
     */
    private function extractPartInfo($message) {
        $partInfo = [
            'name' => null,
            'description' => null,
            'number' => null
        ];

        // البحث عن اسم القطعة
        foreach ($this->commonParts as $part) {
            if (strpos($message, $part) !== false) {
                $partInfo['name'] = $part;
                break;
            }
        }

        // البحث عن رقم القطعة
        preg_match('/\b[A-Z0-9]{6,}\b/', strtoupper($message), $partNumberMatches);
        if (!empty($partNumberMatches)) {
            $partInfo['number'] = $partNumberMatches[0];
        }

        return $partInfo;
    }

    /**
     * تحديد مستوى الأولوية
     */
    private function detectUrgency($message) {
        $highUrgencyKeywords = ['عاجل', 'سريع', 'ضروري', 'urgent', 'asap', 'emergency'];
        $lowUrgencyKeywords = ['متى ما', 'لا يهم', 'مش مستعجل', 'whenever'];

        foreach ($highUrgencyKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return 'high';
            }
        }

        foreach ($lowUrgencyKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return 'low';
            }
        }

        return 'medium';
    }

    /**
     * استخراج الميزانية
     */
    private function extractBudget($message) {
        // البحث عن أرقام مع كلمات تدل على المال
        preg_match('/(\d+)\s*(ريال|درهم|دينار|جنيه|dollar|sr|aed)/', $message, $budgetMatches);
        
        if (!empty($budgetMatches)) {
            return floatval($budgetMatches[1]);
        }

        return null;
    }

    /**
     * توليد رد ذكي
     */
    public function generateResponse($analysis, $conversationContext = []) {
        switch ($analysis['intent']) {
            case 'greeting':
                return $this->generateGreetingResponse();
            
            case 'search_part':
                return $this->generateSearchResponse($analysis);
            
            case 'question':
                return $this->generateQuestionResponse($analysis);
            
            default:
                return $this->generateDefaultResponse();
        }
    }

    private function generateGreetingResponse() {
        $responses = [
            'مرحباً بك! أنا هنا لمساعدتك في العثور على قطع غيار سيارتك. ما هي القطعة التي تبحث عنها؟',
            'أهلاً وسهلاً! كيف يمكنني مساعدتك في البحث عن قطع الغيار اليوم؟',
            'مرحبا! أخبرني عن السيارة والقطعة التي تحتاجها وسأساعدك في العثور عليها.'
        ];
        
        return $responses[array_rand($responses)];
    }

    private function generateSearchResponse($analysis) {
        $response = '';
        
        if ($analysis['car_info']['brand']) {
            $response .= "فهمت أنك تبحث عن قطعة لسيارة {$analysis['car_info']['brand']}";
            
            if ($analysis['car_info']['model']) {
                $response .= " {$analysis['car_info']['model']}";
            }
            
            if ($analysis['car_info']['year']) {
                $response .= " موديل {$analysis['car_info']['year']}";
            }
            
            $response .= ". ";
        }

        if ($analysis['part_info']['name']) {
            $response .= "والقطعة المطلوبة هي: {$analysis['part_info']['name']}. ";
        }

        $response .= "لمساعدتك بشكل أفضل، يرجى تأكيد المعلومات التالية:\n";
        $response .= "1. ماركة السيارة\n";
        $response .= "2. موديل السيارة\n";
        $response .= "3. سنة الصنع\n";
        $response .= "4. اسم القطعة المطلوبة\n";
        $response .= "5. أي تفاصيل إضافية عن القطعة";

        return $response;
    }

    private function generateQuestionResponse($analysis) {
        return 'يمكنني مساعدتك في العثور على قطع غيار سيارتك. أخبرني عن السيارة والقطعة التي تحتاجها وسأتواصل مع المحلات المناسبة للبحث عنها.';
    }

    private function generateDefaultResponse() {
        return 'أعتذر، لم أفهم طلبك بوضوح. يمكنك إخباري عن السيارة والقطعة التي تبحث عنها؟ مثال: "أبحث عن فرامل لتويوتا كامري 2015"';
    }

    /**
     * التحقق من اكتمال المعلومات المطلوبة
     */
    public function isInformationComplete($analysis) {
        return !empty($analysis['car_info']['brand']) && 
               !empty($analysis['part_info']['name']);
    }

    /**
     * توليد أسئلة للحصول على المعلومات الناقصة
     */
    public function generateMissingInfoQuestions($analysis) {
        $questions = [];

        if (empty($analysis['car_info']['brand'])) {
            $questions[] = "ما هي ماركة السيارة؟ (مثل: تويوتا، نيسان، مرسيدس)";
        }

        if (empty($analysis['car_info']['model'])) {
            $questions[] = "ما هو موديل السيارة؟ (مثل: كامري، التيما، C-Class)";
        }

        if (empty($analysis['car_info']['year'])) {
            $questions[] = "ما هي سنة صنع السيارة؟";
        }

        if (empty($analysis['part_info']['name'])) {
            $questions[] = "ما هي القطعة التي تبحث عنها؟ (مثل: فرامل، محرك، بطارية)";
        }

        return $questions;
    }
}
?>
