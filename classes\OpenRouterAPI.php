<?php
require_once 'config/ai_config.php';

/**
 * كلاس للتعامل مع OpenRouter API
 */
class OpenRouterAPI {
    private $baseUrl;
    private $headers;
    private $modelSettings;

    public function __construct() {
        $this->baseUrl = AIConfig::API_BASE_URL;
        $this->headers = AIConfig::getHeaders();
        $this->modelSettings = AIConfig::getDefaultModelSettings();
    }

    /**
     * إرسال طلب للذكاء الاصطناعي
     */
    public function sendChatRequest($messages, $systemPrompt = null) {
        try {
            // إعداد الرسائل (بدون system prompts لتجنب المشاكل)
            $formattedMessages = [];

            // إضافة الرسائل
            if (is_string($messages)) {
                $formattedMessages[] = [
                    'role' => 'user',
                    'content' => $messages
                ];
            } elseif (is_array($messages)) {
                $formattedMessages = $messages;
            }

            // إعداد البيانات للإرسال
            $requestData = array_merge($this->modelSettings, [
                'messages' => $formattedMessages
            ]);

            // إرسال الطلب
            $response = $this->makeRequest('/chat/completions', $requestData);
            
            if ($response && isset($response['choices'][0]['message']['content'])) {
                return [
                    'success' => true,
                    'content' => $response['choices'][0]['message']['content'],
                    'usage' => $response['usage'] ?? null
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Invalid response format',
                    'raw_response' => $response
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * إرسال طلب HTTP
     */
    private function makeRequest($endpoint, $data) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP Error: " . $httpCode . " - " . $response);
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON Decode Error: " . json_last_error_msg());
        }

        return $decodedResponse;
    }

    /**
     * تحليل النص لاستخراج معلومات قطع الغيار
     */
    public function analyzePartRequest($userMessage) {
        // دمج التعليمات مع الرسالة لتجنب مشاكل system prompts
        $fullPrompt = "أنت مساعد ذكي متخصص في قطع غيار السيارات. حلل الرسالة التالية واستخرج المعلومات بصيغة JSON:

رسالة العميل: \"$userMessage\"

أريد منك استخراج المعلومات التالية بصيغة JSON:
{
  \"car_brand\": \"ماركة السيارة\",
  \"car_model\": \"موديل السيارة\",
  \"car_year\": \"سنة الصنع\",
  \"part_name\": \"اسم القطعة\",
  \"part_description\": \"وصف إضافي للقطعة\",
  \"part_number\": \"رقم القطعة إن وجد\",
  \"urgency\": \"low/medium/high\",
  \"budget\": \"الميزانية المقترحة بالأرقام فقط\",
  \"intent\": \"search_part/greeting/question/other\",
  \"confidence\": \"نسبة الثقة من 0 إلى 1\",
  \"missing_info\": [\"قائمة بالمعلومات الناقصة\"]
}

قواعد:
- إذا لم تجد معلومة، ضع null
- الماركات الشائعة: تويوتا، نيسان، هونداي، كيا، مرسيدس، بي ام دبليو، أودي، فورد، شيفروليه
- القطع الشائعة: فرامل، محرك، جير، بطارية، إطار، زيت، فلتر، مصباح، مرآة
- urgency: high للكلمات مثل عاجل/سريع، low لغير مستعجل، medium للباقي
- أجب بـ JSON فقط بدون أي نص إضافي";

        $response = $this->sendChatRequest($fullPrompt);
        
        if ($response['success']) {
            // محاولة تحويل الرد إلى JSON
            $jsonContent = $response['content'];
            
            // تنظيف النص من أي محتوى إضافي
            $jsonContent = preg_replace('/```json\s*/', '', $jsonContent);
            $jsonContent = preg_replace('/```\s*$/', '', $jsonContent);
            $jsonContent = trim($jsonContent);
            
            $analysis = json_decode($jsonContent, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                return [
                    'success' => true,
                    'analysis' => $analysis
                ];
            } else {
                // إذا فشل تحويل JSON، استخدم التحليل المحلي كبديل
                return [
                    'success' => false,
                    'error' => 'JSON parsing failed: ' . json_last_error_msg(),
                    'raw_content' => $jsonContent
                ];
            }
        }
        
        return $response;
    }

    /**
     * توليد رد ذكي للعميل
     */
    public function generateResponse($userMessage, $analysis = null, $conversationHistory = []) {
        // دمج التعليمات مع الرسالة
        $fullPrompt = "أنت مساعد ذكي ومهذب لخدمة العملاء في مجال قطع غيار السيارات.

مهامك:
1. الرد بطريقة ودودة ومهنية
2. مساعدة العملاء في تحديد احتياجاتهم
3. طلب المعلومات الناقصة بطريقة واضحة
4. تأكيد فهم الطلب قبل إرساله للمحلات

قواعد مهمة:
- استخدم اللغة العربية دائماً
- كن مختصراً ومفيداً
- استخدم الرموز التعبيرية بشكل مناسب
- لا تخترع معلومات غير موجودة
- إذا كانت المعلومات مكتملة، أكد الطلب واذكر أنه سيتم إرسال الطلب للمحلات

رسالة العميل: " . $userMessage;

        if ($analysis) {
            $fullPrompt .= "\n\nتحليل الطلب:\n";
            $fullPrompt .= "- السيارة: " . ($analysis['car_brand'] ?? 'غير محدد');
            if ($analysis['car_model']) $fullPrompt .= " " . $analysis['car_model'];
            if ($analysis['car_year']) $fullPrompt .= " (" . $analysis['car_year'] . ")";
            $fullPrompt .= "\n- القطعة: " . ($analysis['part_name'] ?? 'غير محدد');

            if (!empty($analysis['missing_info'])) {
                $fullPrompt .= "\n- معلومات ناقصة: " . implode(', ', $analysis['missing_info']);
            }
        }

        $fullPrompt .= "\n\nاكتب رداً مناسباً للعميل:";

        $response = $this->sendChatRequest($fullPrompt);

        return $response;
    }

    /**
     * تلخيص ردود المحلات
     */
    public function summarizeShopResponses($responses) {
        if (empty($responses)) {
            return [
                'success' => true,
                'content' => 'لم يتم استلام أي ردود من المحلات بعد.'
            ];
        }

        $fullPrompt = "أنت مساعد ذكي. مهمتك تلخيص ردود المحلات على طلب قطعة غيار بطريقة منظمة ومفيدة للعميل.

قواعد:
- استخدم اللغة العربية
- رتب الردود حسب التوفر (المتوفر أولاً)
- اذكر السعر والمحل ومعلومات التواصل
- كن مختصراً ومفيداً
- استخدم رموز تعبيرية مناسبة

ردود المحلات:\n\n";

        foreach ($responses as $i => $response) {
            $fullPrompt .= ($i + 1) . ". محل: " . $response['shop_name'] . "\n";
            $fullPrompt .= "   التوفر: " . ($response['has_part'] ? 'متوفر' : 'غير متوفر') . "\n";
            if ($response['price']) $fullPrompt .= "   السعر: " . $response['price'] . " ريال\n";
            if ($response['delivery_time']) $fullPrompt .= "   وقت التسليم: " . $response['delivery_time'] . "\n";
            if ($response['availability_notes']) $fullPrompt .= "   ملاحظات: " . $response['availability_notes'] . "\n";
            $fullPrompt .= "   الهاتف: " . $response['shop_phone'] . "\n\n";
        }

        $fullPrompt .= "\nاكتب ملخصاً منظماً لهذه الردود:";

        return $this->sendChatRequest($fullPrompt);
    }
}
?>
