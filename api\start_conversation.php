<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../classes/ConversationManager.php';

try {
    $conversationManager = new ConversationManager();
    
    // بدء محادثة جديدة
    $conversationId = $conversationManager->startConversation();
    
    echo json_encode([
        'success' => true,
        'conversation_id' => $conversationId,
        'message' => 'تم بدء المحادثة بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في بدء المحادثة: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
