<?php
require_once 'classes/ShopManager.php';
require_once 'classes/ConversationManager.php';

$shopManager = new ShopManager();
$conversationManager = new ConversationManager();

// معالجة تسجيل الدخول البسيط (يمكن تحسينه لاحقاً)
session_start();
$currentShop = null;

if (isset($_POST['login'])) {
    $shopId = $_POST['shop_id'];
    $shop = $shopManager->getShop($shopId);
    if ($shop) {
        $_SESSION['shop_id'] = $shopId;
        $currentShop = $shop;
    }
}

if (isset($_SESSION['shop_id'])) {
    $currentShop = $shopManager->getShop($_SESSION['shop_id']);
}

if (isset($_POST['logout'])) {
    session_destroy();
    header('Location: shop_dashboard.php');
    exit;
}

// معالجة الرد على الطلب
if (isset($_POST['respond']) && $currentShop) {
    $partRequestId = $_POST['part_request_id'];
    $responseData = [
        'has_part' => $_POST['has_part'] === '1',
        'price' => $_POST['price'] ?? null,
        'notes' => $_POST['notes'] ?? null,
        'delivery_time' => $_POST['delivery_time'] ?? null,
        'contact_info' => $_POST['contact_info'] ?? null
    ];
    
    $shopManager->recordShopResponse($currentShop['id'], $partRequestId, $responseData);
    $successMessage = "تم تسجيل ردك بنجاح!";
}

// الحصول على الطلبات الجديدة
$pendingRequests = [];
if ($currentShop) {
    // هنا يمكن إضافة استعلام للحصول على الطلبات المرسلة لهذا المحل
    // مؤقتاً سنحصل على جميع الطلبات الجديدة
    $sql = "SELECT pr.*, c.customer_name, c.customer_phone, 
                   (SELECT COUNT(*) FROM shop_responses sr WHERE sr.part_request_id = pr.id AND sr.shop_id = ?) as has_responded
            FROM part_requests pr 
            JOIN conversations c ON pr.conversation_id = c.id 
            WHERE c.status = 'waiting_for_shops'
            ORDER BY pr.created_at DESC";
    
    // استخدام قاعدة البيانات مباشرة (يمكن تحسينه)
    require_once 'config/database.php';
    $db = new Database();
    $pendingRequests = $db->fetchAll($sql, [$currentShop['id']]);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المحل - قطع غيار السيارات</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 2rem 0;
        }

        .login-form {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 2rem auto;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .form-group select,
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-danger {
            background: #dc3545;
        }

        .requests-grid {
            display: grid;
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .request-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-right: 4px solid #667eea;
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .request-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-item i {
            color: #667eea;
            width: 20px;
        }

        .response-form {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-top: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-responded {
            background: #d4edda;
            color: #155724;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .request-info {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-store"></i> لوحة تحكم المحل
                </div>
                <?php if ($currentShop): ?>
                <div class="user-info">
                    <span>مرحباً، <?php echo htmlspecialchars($currentShop['owner_name']); ?></span>
                    <span>|</span>
                    <span><?php echo htmlspecialchars($currentShop['name']); ?></span>
                    <form method="post" style="display: inline;">
                        <button type="submit" name="logout" class="btn btn-secondary">تسجيل الخروج</button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-content">
            <?php if (!$currentShop): ?>
                <!-- نموذج تسجيل الدخول -->
                <div class="login-form">
                    <h2 style="text-align: center; margin-bottom: 1.5rem;">تسجيل دخول المحل</h2>
                    <form method="post">
                        <div class="form-group">
                            <label for="shop_id">اختر محلك:</label>
                            <select name="shop_id" id="shop_id" required>
                                <option value="">-- اختر المحل --</option>
                                <?php
                                $shops = $shopManager->getActiveShops();
                                foreach ($shops as $shop):
                                ?>
                                <option value="<?php echo $shop['id']; ?>">
                                    <?php echo htmlspecialchars($shop['name'] . ' - ' . $shop['owner_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" name="login" class="btn" style="width: 100%;">دخول</button>
                    </form>
                </div>
            <?php else: ?>
                <!-- لوحة التحكم الرئيسية -->
                <?php if (isset($successMessage)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $successMessage; ?>
                </div>
                <?php endif; ?>

                <h2><i class="fas fa-list"></i> الطلبات الجديدة</h2>
                
                <?php if (empty($pendingRequests)): ?>
                <div style="text-align: center; padding: 3rem; background: white; border-radius: 10px; margin-top: 1rem;">
                    <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <h3>لا توجد طلبات جديدة</h3>
                    <p>سيتم إشعارك عند وصول طلبات جديدة</p>
                </div>
                <?php else: ?>
                <div class="requests-grid">
                    <?php foreach ($pendingRequests as $request): ?>
                    <div class="request-card">
                        <div class="request-header">
                            <h3><i class="fas fa-wrench"></i> طلب قطعة غيار</h3>
                            <span class="status-badge <?php echo $request['has_responded'] > 0 ? 'status-responded' : 'status-pending'; ?>">
                                <?php echo $request['has_responded'] > 0 ? 'تم الرد' : 'في الانتظار'; ?>
                            </span>
                        </div>
                        
                        <div class="request-info">
                            <div class="info-item">
                                <i class="fas fa-car"></i>
                                <span><strong>السيارة:</strong> <?php echo htmlspecialchars($request['car_brand'] . ' ' . $request['car_model'] . ' ' . $request['car_year']); ?></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-cog"></i>
                                <span><strong>القطعة:</strong> <?php echo htmlspecialchars($request['part_name']); ?></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-user"></i>
                                <span><strong>العميل:</strong> <?php echo htmlspecialchars($request['customer_name'] ?? 'غير محدد'); ?></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span><strong>التاريخ:</strong> <?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?></span>
                            </div>
                        </div>
                        
                        <?php if ($request['part_description']): ?>
                        <div style="margin-bottom: 1rem;">
                            <strong>وصف إضافي:</strong> <?php echo htmlspecialchars($request['part_description']); ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($request['has_responded'] == 0): ?>
                        <!-- نموذج الرد -->
                        <div class="response-form">
                            <h4><i class="fas fa-reply"></i> ردك على الطلب</h4>
                            <form method="post">
                                <input type="hidden" name="part_request_id" value="<?php echo $request['id']; ?>">
                                
                                <div class="form-group">
                                    <label>حالة توفر القطعة:</label>
                                    <select name="has_part" required>
                                        <option value="">-- اختر --</option>
                                        <option value="1">متوفرة</option>
                                        <option value="0">غير متوفرة</option>
                                    </select>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>السعر (ريال):</label>
                                        <input type="number" name="price" step="0.01" placeholder="السعر إذا كانت متوفرة">
                                    </div>
                                    <div class="form-group">
                                        <label>وقت التوصيل:</label>
                                        <input type="text" name="delivery_time" placeholder="مثال: متوفر فوراً، 3 أيام">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>معلومات التواصل الإضافية:</label>
                                    <input type="text" name="contact_info" placeholder="رقم هاتف إضافي أو واتساب">
                                </div>
                                
                                <div class="form-group">
                                    <label>ملاحظات:</label>
                                    <textarea name="notes" rows="3" placeholder="أي ملاحظات إضافية حول القطعة أو التوفر"></textarea>
                                </div>
                                
                                <button type="submit" name="respond" class="btn btn-success">
                                    <i class="fas fa-paper-plane"></i> إرسال الرد
                                </button>
                            </form>
                        </div>
                        <?php else: ?>
                        <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 5px; color: #155724;">
                            <i class="fas fa-check-circle"></i> تم إرسال ردك على هذا الطلب
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // تحديث الصفحة كل دقيقة للتحقق من الطلبات الجديدة
        setInterval(function() {
            if (document.querySelector('.requests-grid')) {
                location.reload();
            }
        }, 60000);
    </script>
</body>
</html>
