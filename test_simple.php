<?php
/**
 * اختبار مبسط للذكاء الاصطناعي
 */

require_once 'classes/OpenRouterAPI.php';

echo "<h1>اختبار مبسط للذكاء الاصطناعي</h1>";
echo "<style>body{font-family:Arial;direction:rtl;}</style>";

$openRouter = new OpenRouterAPI();

// اختبار بسيط للاتصال
echo "<h2>اختبار الاتصال البسيط</h2>";

$testMessage = "مرحبا";
$response = $openRouter->sendChatRequest($testMessage);

if ($response['success']) {
    echo "<div style='color:green;'>✅ نجح الاتصال!</div>";
    echo "<p><strong>الرد:</strong> " . htmlspecialchars($response['content']) . "</p>";
} else {
    echo "<div style='color:red;'>❌ فشل الاتصال!</div>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($response['error']) . "</p>";
}

echo "<hr>";

// اختبار تحليل بسيط
echo "<h2>اختبار التحليل البسيط</h2>";

$analysisPrompt = "حلل هذه الرسالة واستخرج معلومات السيارة والقطعة: 'أبحث عن فرامل لتويوتا كامري 2015'";
$analysisResponse = $openRouter->sendChatRequest($analysisPrompt);

if ($analysisResponse['success']) {
    echo "<div style='color:green;'>✅ نجح التحليل!</div>";
    echo "<p><strong>النتيجة:</strong></p>";
    echo "<div style='background:#f5f5f5;padding:10px;'>" . nl2br(htmlspecialchars($analysisResponse['content'])) . "</div>";
} else {
    echo "<div style='color:red;'>❌ فشل التحليل!</div>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($analysisResponse['error']) . "</p>";
}

echo "<hr>";

// اختبار توليد رد
echo "<h2>اختبار توليد الرد</h2>";

$responsePrompt = "أنت مساعد لقطع غيار السيارات. رد على العميل الذي قال: 'أريد بطارية لسيارتي'";
$replyResponse = $openRouter->sendChatRequest($responsePrompt);

if ($replyResponse['success']) {
    echo "<div style='color:green;'>✅ نجح توليد الرد!</div>";
    echo "<p><strong>الرد المولد:</strong></p>";
    echo "<div style='background:#f0fff0;padding:10px;border:1px solid #ccc;'>" . nl2br(htmlspecialchars($replyResponse['content'])) . "</div>";
} else {
    echo "<div style='color:red;'>❌ فشل توليد الرد!</div>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($replyResponse['error']) . "</p>";
}

echo "<hr>";
echo "<h2>انتهى الاختبار المبسط</h2>";
echo "<p>إذا نجحت جميع الاختبارات أعلاه، فالنظام جاهز للاستخدام!</p>";
?>
