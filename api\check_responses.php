<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../classes/ConversationManager.php';
require_once '../classes/ShopManager.php';

try {
    if (!isset($_GET['conversation_id'])) {
        throw new Exception('معرف المحادثة مطلوب');
    }
    
    $conversationId = $_GET['conversation_id'];
    
    $conversationManager = new ConversationManager();
    $shopManager = new ShopManager();
    
    // الحصول على طلبات قطع الغيار للمحادثة
    $partRequests = $conversationManager->getPartRequests($conversationId);
    
    $newResponses = [];
    
    foreach ($partRequests as $request) {
        // الحصول على ردود المحلات
        $shopResponses = $shopManager->getShopResponses($request['id']);
        
        foreach ($shopResponses as $response) {
            // التحقق من أن هذا الرد لم يتم إرساله للعميل من قبل
            // (يمكن إضافة جدول لتتبع الردود المرسلة)
            
            $responseMessage = "📍 رد من {$response['shop_name']}:\n\n";
            
            if ($response['has_part']) {
                $responseMessage .= "✅ القطعة متوفرة!\n";
                if ($response['price']) {
                    $responseMessage .= "💰 السعر: {$response['price']} ريال\n";
                }
                if ($response['delivery_time']) {
                    $responseMessage .= "⏰ وقت التوصيل: {$response['delivery_time']}\n";
                }
                if ($response['contact_info']) {
                    $responseMessage .= "📞 للتواصل: {$response['contact_info']}\n";
                }
            } else {
                $responseMessage .= "❌ القطعة غير متوفرة حالياً\n";
            }
            
            if ($response['availability_notes']) {
                $responseMessage .= "📝 ملاحظات: {$response['availability_notes']}\n";
            }
            
            $responseMessage .= "\n📍 العنوان: {$response['shop_address']}";
            $responseMessage .= "\n📞 الهاتف: {$response['shop_phone']}";
            
            $newResponses[] = [
                'message' => $responseMessage,
                'shop_id' => $response['shop_id'],
                'has_part' => $response['has_part']
            ];
            
            // إضافة الرد كرسالة في المحادثة
            $conversationManager->addMessage($conversationId, 'bot', $responseMessage);
        }
    }
    
    // إذا تم استلام جميع الردود، تحديث حالة المحادثة
    if (!empty($newResponses)) {
        $conversationManager->updateConversationStatus($conversationId, 'completed');
        
        // إضافة رسالة ختامية
        $finalMessage = "\n🎉 تم استلام جميع الردود من المحلات!\n";
        $finalMessage .= "يمكنك التواصل مباشرة مع المحل المناسب لك.\n";
        $finalMessage .= "شكراً لاستخدام خدمتنا! 😊";
        
        $conversationManager->addMessage($conversationId, 'bot', $finalMessage);
        
        $newResponses[] = [
            'message' => $finalMessage,
            'shop_id' => null,
            'has_part' => null
        ];
    }
    
    echo json_encode([
        'success' => true,
        'new_responses' => $newResponses,
        'count' => count($newResponses)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في التحقق من الردود: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
